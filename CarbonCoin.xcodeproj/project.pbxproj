// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		AE1D42792E4F5DE000B8B171 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = AE1D42612E4F5DDE00B8B171 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AE1D42682E4F5DDE00B8B171;
			remoteInfo = CarbonCoin;
		};
		AE1D42832E4F5DE000B8B171 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = AE1D42612E4F5DDE00B8B171 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AE1D42682E4F5DDE00B8B171;
			remoteInfo = CarbonCoin;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		AE1D42692E4F5DDE00B8B171 /* CarbonCoin.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CarbonCoin.app; sourceTree = BUILT_PRODUCTS_DIR; };
		AE1D42782E4F5DE000B8B171 /* CarbonCoinTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CarbonCoinTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		AE1D42822E4F5DE000B8B171 /* CarbonCoinUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CarbonCoinUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		AE1D426B2E4F5DDE00B8B171 /* CarbonCoin */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CarbonCoin;
			sourceTree = "<group>";
		};
		AE1D427B2E4F5DE000B8B171 /* CarbonCoinTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CarbonCoinTests;
			sourceTree = "<group>";
		};
		AE1D42852E4F5DE000B8B171 /* CarbonCoinUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CarbonCoinUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		AE1D42662E4F5DDE00B8B171 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AE1D42752E4F5DE000B8B171 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AE1D427F2E4F5DE000B8B171 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		AE1D42602E4F5DDE00B8B171 = {
			isa = PBXGroup;
			children = (
				AE1D426B2E4F5DDE00B8B171 /* CarbonCoin */,
				AE1D427B2E4F5DE000B8B171 /* CarbonCoinTests */,
				AE1D42852E4F5DE000B8B171 /* CarbonCoinUITests */,
				AE1D426A2E4F5DDE00B8B171 /* Products */,
			);
			sourceTree = "<group>";
		};
		AE1D426A2E4F5DDE00B8B171 /* Products */ = {
			isa = PBXGroup;
			children = (
				AE1D42692E4F5DDE00B8B171 /* CarbonCoin.app */,
				AE1D42782E4F5DE000B8B171 /* CarbonCoinTests.xctest */,
				AE1D42822E4F5DE000B8B171 /* CarbonCoinUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		AE1D42682E4F5DDE00B8B171 /* CarbonCoin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AE1D428C2E4F5DE000B8B171 /* Build configuration list for PBXNativeTarget "CarbonCoin" */;
			buildPhases = (
				AE1D42652E4F5DDE00B8B171 /* Sources */,
				AE1D42662E4F5DDE00B8B171 /* Frameworks */,
				AE1D42672E4F5DDE00B8B171 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				AE1D426B2E4F5DDE00B8B171 /* CarbonCoin */,
			);
			name = CarbonCoin;
			packageProductDependencies = (
			);
			productName = CarbonCoin;
			productReference = AE1D42692E4F5DDE00B8B171 /* CarbonCoin.app */;
			productType = "com.apple.product-type.application";
		};
		AE1D42772E4F5DE000B8B171 /* CarbonCoinTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AE1D428F2E4F5DE000B8B171 /* Build configuration list for PBXNativeTarget "CarbonCoinTests" */;
			buildPhases = (
				AE1D42742E4F5DE000B8B171 /* Sources */,
				AE1D42752E4F5DE000B8B171 /* Frameworks */,
				AE1D42762E4F5DE000B8B171 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AE1D427A2E4F5DE000B8B171 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				AE1D427B2E4F5DE000B8B171 /* CarbonCoinTests */,
			);
			name = CarbonCoinTests;
			packageProductDependencies = (
			);
			productName = CarbonCoinTests;
			productReference = AE1D42782E4F5DE000B8B171 /* CarbonCoinTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		AE1D42812E4F5DE000B8B171 /* CarbonCoinUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AE1D42922E4F5DE000B8B171 /* Build configuration list for PBXNativeTarget "CarbonCoinUITests" */;
			buildPhases = (
				AE1D427E2E4F5DE000B8B171 /* Sources */,
				AE1D427F2E4F5DE000B8B171 /* Frameworks */,
				AE1D42802E4F5DE000B8B171 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AE1D42842E4F5DE000B8B171 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				AE1D42852E4F5DE000B8B171 /* CarbonCoinUITests */,
			);
			name = CarbonCoinUITests;
			packageProductDependencies = (
			);
			productName = CarbonCoinUITests;
			productReference = AE1D42822E4F5DE000B8B171 /* CarbonCoinUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		AE1D42612E4F5DDE00B8B171 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					AE1D42682E4F5DDE00B8B171 = {
						CreatedOnToolsVersion = 16.3;
					};
					AE1D42772E4F5DE000B8B171 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = AE1D42682E4F5DDE00B8B171;
					};
					AE1D42812E4F5DE000B8B171 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = AE1D42682E4F5DDE00B8B171;
					};
				};
			};
			buildConfigurationList = AE1D42642E4F5DDE00B8B171 /* Build configuration list for PBXProject "CarbonCoin" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = AE1D42602E4F5DDE00B8B171;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = AE1D426A2E4F5DDE00B8B171 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				AE1D42682E4F5DDE00B8B171 /* CarbonCoin */,
				AE1D42772E4F5DE000B8B171 /* CarbonCoinTests */,
				AE1D42812E4F5DE000B8B171 /* CarbonCoinUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AE1D42672E4F5DDE00B8B171 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AE1D42762E4F5DE000B8B171 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AE1D42802E4F5DE000B8B171 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AE1D42652E4F5DDE00B8B171 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AE1D42742E4F5DE000B8B171 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AE1D427E2E4F5DE000B8B171 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		AE1D427A2E4F5DE000B8B171 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AE1D42682E4F5DDE00B8B171 /* CarbonCoin */;
			targetProxy = AE1D42792E4F5DE000B8B171 /* PBXContainerItemProxy */;
		};
		AE1D42842E4F5DE000B8B171 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AE1D42682E4F5DDE00B8B171 /* CarbonCoin */;
			targetProxy = AE1D42832E4F5DE000B8B171 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		AE1D428A2E4F5DE000B8B171 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		AE1D428B2E4F5DE000B8B171 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		AE1D428D2E4F5DE000B8B171 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9SBGQ83M2P;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ffffy.CarbonCoin;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AE1D428E2E4F5DE000B8B171 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9SBGQ83M2P;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ffffy.CarbonCoin;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AE1D42902E4F5DE000B8B171 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ffffy.CarbonCoinTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CarbonCoin.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CarbonCoin";
			};
			name = Debug;
		};
		AE1D42912E4F5DE000B8B171 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ffffy.CarbonCoinTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CarbonCoin.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CarbonCoin";
			};
			name = Release;
		};
		AE1D42932E4F5DE000B8B171 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ffffy.CarbonCoinUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = CarbonCoin;
			};
			name = Debug;
		};
		AE1D42942E4F5DE000B8B171 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ffffy.CarbonCoinUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = CarbonCoin;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		AE1D42642E4F5DDE00B8B171 /* Build configuration list for PBXProject "CarbonCoin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AE1D428A2E4F5DE000B8B171 /* Debug */,
				AE1D428B2E4F5DE000B8B171 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AE1D428C2E4F5DE000B8B171 /* Build configuration list for PBXNativeTarget "CarbonCoin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AE1D428D2E4F5DE000B8B171 /* Debug */,
				AE1D428E2E4F5DE000B8B171 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AE1D428F2E4F5DE000B8B171 /* Build configuration list for PBXNativeTarget "CarbonCoinTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AE1D42902E4F5DE000B8B171 /* Debug */,
				AE1D42912E4F5DE000B8B171 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AE1D42922E4F5DE000B8B171 /* Build configuration list for PBXNativeTarget "CarbonCoinUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AE1D42932E4F5DE000B8B171 /* Debug */,
				AE1D42942E4F5DE000B8B171 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = AE1D42612E4F5DDE00B8B171 /* Project object */;
}
