//
//  ColorExtensions.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

// MARK: - Color Extensions
extension Color {
    
    // MARK: - Brand Colors
    /// 主品牌绿色 #61D7B9
    static let brandGreen = Color("CCGreen")

    /// 辅助黄色 #B0EB67
    static let auxiliaryYellow = Color("auxiliaryYellow")

    /// 天空蓝色
    static let skyBlue = Color("skyBlue")

    /// 主品牌色
    static let brandColor = Color("brandColor")

    // MARK: - New Design System Colors
    /// 默认按钮背景色 #242720
    static let defaultButtonBackground = Color("defaultButtonBackground")

    /// 卡片背景色 #1F1F1F
    static let cardBackgroundColor = Color("cardBackgroundColor")

    // MARK: - Global Background Colors
    /// 全局背景渐变色点1 #010101
    static let backgroundDark1 = Color("backgroundDark1")

    /// 全局背景渐变色点2 #000000
    static let backgroundDark2 = Color("backgroundDark2")

    /// 全局背景渐变色点3 #2E4F17
    static let backgroundGreen1 = Color("backgroundGreen1")

    /// 全局背景渐变色点4 #2A370C
    static let backgroundGreen2 = Color("backgroundGreen2")

    // MARK: - Border Gradient Colors
    /// 边框渐变色1 - 白色半透明
    static let borderGradient1 = Color("borderGradient1")

    /// 边框渐变色2 - 绿色半透明 #4B7905
    static let borderGradient2 = Color("borderGradient2")

    /// 边框渐变色3 - 黄绿色半透明 #FDFF81
    static let borderGradient3 = Color("borderGradient3")

    /// 边框渐变色4 - 绿色半透明 #A8D200
    static let borderGradient4 = Color("borderGradient4")
    
    // MARK: - Gradient Colors
    /// 主渐变色 - 按钮选中状态：从品牌绿到辅助黄 (180deg, #61D7B9 0%, #B0EB67 100%)
    static let primaryGradient = LinearGradient(
        gradient: Gradient(colors: [brandGreen, auxiliaryYellow]),
        startPoint: .top,
        endPoint: .bottom
    )

    /// 反向主渐变色：从辅助黄到品牌绿
    static let primaryGradientReversed = LinearGradient(
        gradient: Gradient(colors: [auxiliaryYellow, brandGreen]),
        startPoint: .top,
        endPoint: .bottom
    )

    /// 水平主渐变色：从左到右
    static let primaryGradientHorizontal = LinearGradient(
        gradient: Gradient(colors: [brandGreen, auxiliaryYellow]),
        startPoint: .leading,
        endPoint: .trailing
    )

    /// 对角线渐变色：从左上到右下
    static let primaryGradientDiagonal = LinearGradient(
        gradient: Gradient(colors: [brandGreen, auxiliaryYellow]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    /// 天空渐变色：从天空蓝到品牌绿
    static let skyGradient = LinearGradient(
        gradient: Gradient(colors: [skyBlue, brandGreen]),
        startPoint: .top,
        endPoint: .bottom
    )

    // MARK: - New Design System Gradients
    /// 全局背景圆锥渐变 - 模拟圆锥渐变效果
    /// 原始CSS: conic-gradient(from 189.17deg at 42.81% 48.91%, #010101 0deg, #000000 1.73deg, #2E4F17 133.27deg, #2A370C 188.65deg, #010101 360deg)
    static let globalBackgroundGradient = RadialGradient(
        gradient: Gradient(stops: [
            .init(color: backgroundDark1, location: 0.0),
            .init(color: backgroundDark2, location: 0.1),
            .init(color: backgroundGreen1, location: 0.6),
            .init(color: backgroundGreen2, location: 0.8),
            .init(color: backgroundDark1, location: 1.0)
        ]),
        center: UnitPoint(x: 0.43, y: 0.49),
        startRadius: 50,
        endRadius: 400
    )

    /// 卡片边框渐变 - 180度线性渐变
    /// 原始CSS: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(75, 121, 5, 0.5) 30.77%, rgba(253, 255, 129, 0.5) 66.83%, rgba(168, 210, 0, 0.5) 90.38%)
    static let cardBorderGradient = LinearGradient(
        gradient: Gradient(stops: [
            .init(color: borderGradient1, location: 0.0),
            .init(color: borderGradient2, location: 0.31),
            .init(color: borderGradient3, location: 0.67),
            .init(color: borderGradient4, location: 0.90)
        ]),
        startPoint: .top,
        endPoint: .bottom
    )
    
    // MARK: - UI Colors
    /// 背景色 - 深绿色
    static let backgroundDark = Color(red: 0.2, green: 0.3, blue: 0.2)
    
    /// 卡片背景色 - 半透明白色
    static let cardBackground = Color.white.opacity(0.1)
    
    /// 玻璃效果背景色
    static let glassBackground = Color.white.opacity(0.15)
    
    /// 文字主色 - 白色
    static let textPrimary = Color.white
    
    /// 文字次要色 - 半透明白色
    static let textSecondary = Color.white.opacity(0.7)
    
    /// 文字禁用色 - 更透明的白色
    static let textDisabled = Color.white.opacity(0.4)
    
    // MARK: - Status Colors
    /// 成功色 - 绿色
    static let success = Color.green
    
    /// 警告色 - 橙色
    static let warning = Color.orange
    
    /// 错误色 - 红色
    static let error = Color.red
    
    /// 信息色 - 蓝色
    static let info = Color.blue
}

// MARK: - Gradient Extensions
extension LinearGradient {
    
    /// 创建角度渐变
    /// - Parameters:
    ///   - colors: 渐变颜色数组
    ///   - angle: 角度（度数）
    /// - Returns: 线性渐变
    static func angle(_ colors: [Color], angle: Double) -> LinearGradient {
        let radians = angle * .pi / 180
        let x = cos(radians)
        let y = sin(radians)
        
        return LinearGradient(
            gradient: Gradient(colors: colors),
            startPoint: UnitPoint(x: 0.5 - x/2, y: 0.5 - y/2),
            endPoint: UnitPoint(x: 0.5 + x/2, y: 0.5 + y/2)
        )
    }
    
    /// 主品牌渐变 - 180度
    static let primaryBrand = LinearGradient.angle([.brandGreen, .auxiliaryYellow], angle: 180)
    
    /// 主品牌渐变 - 45度对角线
    static let primaryBrandDiagonal = LinearGradient.angle([.brandGreen, .auxiliaryYellow], angle: 45)
}

// MARK: - Shadow Extensions
extension View {
    
    /// 应用主要阴影效果
    func primaryShadow() -> some View {
        self.shadow(
            color: Color.black.opacity(0.2),
            radius: 8,
            x: 0,
            y: 4
        )
    }
    
    /// 应用卡片阴影效果
    func cardShadow() -> some View {
        self.shadow(
            color: Color.black.opacity(0.1),
            radius: 4,
            x: 0,
            y: 2
        )
    }
    
    /// 应用浮动阴影效果
    func floatingShadow() -> some View {
        self.shadow(
            color: Color.black.opacity(0.3),
            radius: 12,
            x: 0,
            y: 6
        )
    }
}
