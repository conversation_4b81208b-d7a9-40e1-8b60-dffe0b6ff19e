//
//  MainTabView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 0

    // Tab配置
    private let tabItems = [
        TabItem(iconName: "footIcon", title: "足迹", tag: 0),
        TabItem(iconName: "petIcon", title: "宠物", tag: 1),
        TabItem(iconName: "scanIcon", title: "扫码", tag: 2),
        TabItem(iconName: "chatIcon", title: "聊天", tag: 3),
        TabItem(iconName: "meIcon", title: "设置", tag: 4)
    ]

    var body: some View {
        CustomTabBarContainer(selectedTab: $selectedTab, tabItems: tabItems) {
            ZStack {
                // 背景渐变
                Color.backgroundDark
                    .ignoresSafeArea()

                // 页面内容
                Group {
                    switch selectedTab {
                    case 0:
                        FootprintView()
                    case 1:
                        PetView()
                    case 2:
                        ScanView()
                    case 3:
                        ChatView()
                    case 4:
                        SettingsView()
                    default:
                        FootprintView()
                    }
                }
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
                .animation(Theme.AnimationStyle.easeInOut, value: selectedTab)
            }
        }
    }
}

#Preview {
    MainTabView()
}
