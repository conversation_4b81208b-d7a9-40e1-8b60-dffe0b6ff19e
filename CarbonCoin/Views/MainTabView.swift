//
//  MainTabView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct MainTabView: View {
    var body: some View {
        TabView {
            // 足迹页面
            FootprintView()
                .tabItem {
                    Image(systemName: "location.fill")
                    Text("足迹")
                }
            
            // 宠物页面
            PetView()
                .tabItem {
                    Image(systemName: "pawprint.fill")
                    Text("宠物")
                }
            
            // 扫码页面
            ScanView()
                .tabItem {
                    Image(systemName: "qrcode.viewfinder")
                    Text("扫码")
                }
            
            // 聊天页面
            ChatView()
                .tabItem {
                    Image(systemName: "message.fill")
                    Text("聊天")
                }
            
            // 设置页面
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("设置")
                }
        }
        .accentColor(.green) // 设置选中状态的颜色为绿色
    }
}

#Preview {
    MainTabView()
}
