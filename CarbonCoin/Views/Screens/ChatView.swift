//
//  ChatView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct ChatView: View {
    @State private var rotation: Double = 0
    @State private var glowOpacity: Double = 0.3

    var body: some View {
        NavigationStack {
            VStack(spacing: Theme.Spacing.xl) {
                Spacer()

                // 旋转地球动画容器
                ZStack {
                    // 背景光环效果
                    Circle()
                        .fill(Color.primaryGradient.opacity(glowOpacity))
                        .frame(width: 200, height: 200)
                        .onAppear {
                            withAnimation(
                                Animation.easeInOut(duration: 2.0)
                                    .repeatForever(autoreverses: true)
                            ) {
                                glowOpacity = 0.6
                            }
                        }

                    // 旋转地球
                    Image(systemName: "globe")
                        .font(.system(size: 80))
                        .foregroundColor(.auxiliaryYellow)
                        .rotationEffect(.degrees(rotation))
                        .onAppear {
                            withAnimation(
                                Animation.linear(duration: 10)
                                    .repeatForever(autoreverses: false)
                            ) {
                                rotation = 360
                            }
                        }
                }

                // 文字信息
                VStack(spacing: Theme.Spacing.md) {
                    Text("聊天功能开发中")
                        .font(.title1Brand)
                        .foregroundColor(.textPrimary)
                        .gradientText()

                    Text("敬请期待")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)

                    // 功能预告卡片
                    VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                        Text("即将推出")
                            .font(.title3Brand)
                            .foregroundColor(.textPrimary)

                        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                            FeatureRow(icon: "message.fill", text: "好友实时聊天")
                            FeatureRow(icon: "photo.fill", text: "分享沿途风景")
                            FeatureRow(icon: "trophy.fill", text: "成就分享")
                            FeatureRow(icon: "heart.fill", text: "互动表情")
                        }
                    }
                    .padding(Theme.Spacing.lg)
                    .glassCard()
                }

                Spacer()
                Spacer(minLength: 100) // 为底部TabBar留出空间
            }
            .padding(.horizontal, Theme.Spacing.md)
            .navigationTitle("聊天")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.hidden, for: .navigationBar)
        }
    }
}

// MARK: - Feature Row
struct FeatureRow: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(spacing: Theme.Spacing.sm) {
            Image(systemName: icon)
                .foregroundColor(.auxiliaryYellow)
                .frame(width: 20)

            Text(text)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)

            Spacer()
        }
    }
}

#Preview {
    ZStack {
        Color.backgroundDark.ignoresSafeArea()
        ChatView()
    }
}
