//
//  ChatView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct ChatView: View {
    @State private var rotation: Double = 0
    
    var body: some View {
        NavigationStack {
            VStack {
                Spacer()
                
                // 旋转地球动画
                Image(systemName: "globe")
                    .font(.system(size: 80))
                    .foregroundColor(.green)
                    .rotationEffect(.degrees(rotation))
                    .onAppear {
                        withAnimation(.linear(duration: 10).repeatForever(autoreverses: false)) {
                            rotation = 360
                        }
                    }
                
                Text("聊天功能开发中")
                    .font(.title)
                    .foregroundColor(.green)
                    .padding(.top, 20)
                
                Text("敬请期待")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("聊天")
        }
    }
}

#Preview {
    ChatView()
}
