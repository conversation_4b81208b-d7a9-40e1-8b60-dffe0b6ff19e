//
//  FootprintView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct FootprintView: View {
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: Theme.Spacing.lg) {
                    // 顶部用户问候区域
                    UserGreetingCard()

                    // 公告卡片
                    AnnouncementCard()

                    // 地图入口按钮
                    MapEntryButton()

                    // 图表区域占位
                    ChartPlaceholder()

                    Spacer(minLength: 100) // 为底部TabBar留出空间
                }
                .padding(.horizontal, Theme.Spacing.md)
                .padding(.top, Theme.Spacing.md)
            }
            .navigationTitle("足迹")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.hidden, for: .navigationBar)
        }
    }
}

// MARK: - User Greeting Card
struct UserGreetingCard: View {
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                Text("Hi, 用户")
                    .font(.title2Brand)
                    .foregroundColor(.textPrimary)

                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.auxiliaryYellow)

                    Text("碳币: 128")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)

                    Spacer()

                    HStack(spacing: 4) {
                        Text("↗ 12%")
                            .font(.captionBrand)
                            .foregroundColor(.success)

                        Image(systemName: "arrow.up.right")
                            .font(.caption)
                            .foregroundColor(.success)
                    }
                }
            }

            Spacer()
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Announcement Card
struct AnnouncementCard: View {
    var body: some View {
        HStack {
            Image(systemName: "info.circle.fill")
                .foregroundColor(.skyBlue)
                .font(.title3)

            VStack(alignment: .leading, spacing: 4) {
                Text("系统公告")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Text("敏感地图已经维修完毕！")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }

            Spacer()
        }
        .padding(Theme.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .stroke(Color.skyBlue.opacity(0.3), lineWidth: 2)
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .fill(Color.skyBlue.opacity(0.1))
                )
        )
    }
}

// MARK: - Map Entry Button
struct MapEntryButton: View {
    var body: some View {
        Button(action: {
            // TODO: 打开地图页面
        }) {
            HStack {
                Image(systemName: "map.fill")
                    .font(.title2)
                    .foregroundColor(.textPrimary)

                VStack(alignment: .leading) {
                    Text("探索地图")
                        .font(.title3Brand)
                        .foregroundColor(.textPrimary)

                    Text("查看轨迹和好友位置")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(.textSecondary)
            }
            .padding(Theme.Spacing.lg)
        }
        .buttonStyle(PrimaryButtonStyle())
    }
}

// MARK: - Chart Placeholder
struct ChartPlaceholder: View {
    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            HStack {
                Text("碳足迹统计")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Spacer()

                // Tab选择器占位
                HStack(spacing: Theme.Spacing.sm) {
                    ForEach(["24h", "周", "月", "6月"], id: \.self) { period in
                        Text(period)
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                            .padding(.horizontal, Theme.Spacing.sm)
                            .padding(.vertical, Theme.Spacing.xs)
                            .background(
                                RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                                    .fill(Color.glassBackground)
                            )
                    }
                }
            }

            // 图表占位区域
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.glassBackground)
                .frame(height: 200)
                .overlay(
                    VStack {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .font(.largeTitle)
                            .foregroundColor(.textSecondary)

                        Text("图表功能开发中")
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                    }
                )
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

#Preview {
    ZStack {
        Color.backgroundDark.ignoresSafeArea()
        FootprintView()
    }
}
