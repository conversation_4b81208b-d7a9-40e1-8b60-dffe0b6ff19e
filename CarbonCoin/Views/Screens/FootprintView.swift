//
//  FootprintView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct FootprintView: View {
    var body: some View {
        NavigationStack {
            VStack {
                Text("足迹页面")
                    .font(.largeTitle)
                    .foregroundColor(.green)
                
                Spacer()
                
                Text("地图、轨迹记录、好友位置等功能")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
            }
            .navigationTitle("足迹")
        }
    }
}

#Preview {
    FootprintView()
}
