//
//  PetView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct PetView: View {
    @State private var petScale: CGFloat = 1.0
    @State private var isFeeding = false

    var body: some View {
        NavigationStack {
            VStack(spacing: Theme.Spacing.xl) {
                Spacer()

                // 宠物形象
                PetImageView(scale: $petScale, isFeeding: $isFeeding)

                // 宠物信息卡片
                PetInfoCard()

                // 经验条
                ExperienceBar()

                // 喂养按钮
                FeedButton(isFeeding: $isFeeding, petScale: $petScale)

                Spacer()
                Spacer(minLength: 100) // 为底部TabBar留出空间
            }
            .padding(.horizontal, Theme.Spacing.md)
            .navigationTitle("宠物")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.hidden, for: .navigationBar)
        }
    }
}

// MARK: - Pet Image View
struct PetImageView: View {
    @Binding var scale: CGFloat
    @Binding var isFeeding: Bool

    var body: some View {
        ZStack {
            // 背景光环效果
            Circle()
                .fill(Color.primaryGradient.opacity(0.3))
                .frame(width: 200, height: 200)
                .scaleEffect(isFeeding ? 1.2 : 1.0)
                .animation(Theme.AnimationStyle.normal, value: isFeeding)

            // 宠物图像占位
            Circle()
                .fill(Color.glassBackground)
                .frame(width: 150, height: 150)
                .overlay(
                    VStack {
                        Image(systemName: "pawprint.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.auxiliaryYellow)

                        Text("小碳")
                            .font(.title3Brand)
                            .foregroundColor(.textPrimary)
                    }
                )
                .scaleEffect(scale)
                .animation(Theme.AnimationStyle.bouncy, value: scale)
        }
    }
}

// MARK: - Pet Info Card
struct PetInfoCard: View {
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                Text("小碳 Lv.1")
                    .font(.title2Brand)
                    .foregroundColor(.textPrimary)

                Text("可爱的碳宠物")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: Theme.Spacing.sm) {
                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.auxiliaryYellow)
                    Text("喂养成本: 5")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }

                Text("上次喂养: 2小时前")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Experience Bar
struct ExperienceBar: View {
    private let currentExp = 45
    private let maxExp = 100

    var progress: Double {
        Double(currentExp) / Double(maxExp)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            HStack {
                Text("经验值")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("\(currentExp)/\(maxExp)")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }

            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景条
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                        .fill(Color.glassBackground)
                        .frame(height: 8)

                    // 进度条
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                        .fill(Color.primaryGradient)
                        .frame(width: geometry.size.width * progress, height: 8)
                        .animation(Theme.AnimationStyle.easeInOut, value: progress)
                }
            }
            .frame(height: 8)
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Feed Button
struct FeedButton: View {
    @Binding var isFeeding: Bool
    @Binding var petScale: CGFloat

    var body: some View {
        Button(action: feedPet) {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.textPrimary)

                Text("喂养宠物")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                if isFeeding {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(PrimaryButtonStyle())
        .disabled(isFeeding)
    }

    private func feedPet() {
        isFeeding = true

        // 宠物缩放动画
        withAnimation(Theme.AnimationStyle.bouncy) {
            petScale = 1.2
        }

        // 模拟喂养过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation(Theme.AnimationStyle.bouncy) {
                petScale = 1.0
                isFeeding = false
            }
        }
    }
}

#Preview {
    ZStack {
        Color.backgroundDark.ignoresSafeArea()
        PetView()
    }
}
