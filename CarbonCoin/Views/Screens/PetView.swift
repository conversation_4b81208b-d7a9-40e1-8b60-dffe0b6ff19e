//
//  PetView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct PetView: View {
    var body: some View {
        NavigationStack {
            VStack {
                Text("宠物页面")
                    .font(.largeTitle)
                    .foregroundColor(.green)
                
                Spacer()
                
                Text("碳宠展示、喂养、进化等功能")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
            }
            .navigationTitle("宠物")
        }
    }
}

#Preview {
    PetView()
}
