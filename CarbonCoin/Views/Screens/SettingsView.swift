//
//  SettingsView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct SettingsView: View {
    @State private var shareLocation = true
    @State private var pushNotifications = true
    @State private var searchText = ""

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: Theme.Spacing.lg) {
                    // 用户信息卡片
                    UserProfileCard()

                    // 好友管理
                    FriendManagementSection(searchText: $searchText)

                    // 隐私设置
                    PrivacySettingsSection(
                        shareLocation: $shareLocation,
                        pushNotifications: $pushNotifications
                    )

                    // 其他设置
                    OtherSettingsSection()

                    Spacer(minLength: 100) // 为底部TabBar留出空间
                }
                .padding(.horizontal, Theme.Spacing.md)
                .padding(.top, Theme.Spacing.md)
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.hidden, for: .navigationBar)
        }
    }
}

// MARK: - User Profile Card
struct UserProfileCard: View {
    var body: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 头像
            Circle()
                .fill(Color.primaryGradient)
                .frame(width: 60, height: 60)
                .overlay(
                    Image(systemName: "person.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                )

            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                Text("用户名")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Text("ID: 123456789")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)

                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.auxiliaryYellow)
                        .font(.caption)

                    Text("碳币: 128")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
            }

            Spacer()

            Button(action: {}) {
                Image(systemName: "pencil")
                    .foregroundColor(.textSecondary)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Friend Management Section
struct FriendManagementSection: View {
    @Binding var searchText: String

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("好友管理")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.textSecondary)

                TextField("搜索用户名", text: $searchText)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                    .textFieldStyle(PlainTextFieldStyle())
            }
            .padding(Theme.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                    .fill(Color.glassBackground)
            )

            // 好友列表占位
            VStack(spacing: Theme.Spacing.sm) {
                FriendRow(name: "小明", status: "在线", isOnline: true)
                FriendRow(name: "小红", status: "2小时前", isOnline: false)
                FriendRow(name: "小李", status: "在线", isOnline: true)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Friend Row
struct FriendRow: View {
    let name: String
    let status: String
    let isOnline: Bool

    var body: some View {
        HStack {
            // 头像
            Circle()
                .fill(Color.primaryGradient.opacity(0.7))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(String(name.prefix(1)))
                        .font(.bodyBrand)
                        .foregroundColor(.white)
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(name)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Text(status)
                    .font(.captionBrand)
                    .foregroundColor(isOnline ? .success : .textSecondary)
            }

            Spacer()

            // 在线状态指示器
            Circle()
                .fill(isOnline ? Color.success : Color.textSecondary)
                .frame(width: 8, height: 8)
        }
        .padding(.vertical, Theme.Spacing.xs)
    }
}

// MARK: - Privacy Settings Section
struct PrivacySettingsSection: View {
    @Binding var shareLocation: Bool
    @Binding var pushNotifications: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("隐私设置")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.md) {
                SettingToggleRow(
                    icon: "location.fill",
                    title: "位置共享",
                    description: "允许好友查看我的位置",
                    isOn: $shareLocation
                )

                SettingToggleRow(
                    icon: "bell.fill",
                    title: "推送通知",
                    description: "接收好友消息和系统通知",
                    isOn: $pushNotifications
                )
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Setting Toggle Row
struct SettingToggleRow: View {
    let icon: String
    let title: String
    let description: String
    @Binding var isOn: Bool

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.auxiliaryYellow)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Text(description)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: Color.brandGreen))
        }
    }
}

// MARK: - Other Settings Section
struct OtherSettingsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("其他")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.sm) {
                SettingRow(icon: "info.circle", title: "关于应用", subtitle: "版本 1.0.0")
                SettingRow(icon: "envelope", title: "意见反馈", subtitle: "")
                SettingRow(icon: "questionmark.circle", title: "帮助中心", subtitle: "")
                SettingRow(icon: "arrow.right.square", title: "退出登录", subtitle: "", isDestructive: true)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Setting Row
struct SettingRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let isDestructive: Bool

    init(icon: String, title: String, subtitle: String, isDestructive: Bool = false) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.isDestructive = isDestructive
    }

    var body: some View {
        Button(action: {}) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(isDestructive ? .error : .auxiliaryYellow)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.bodyBrand)
                        .foregroundColor(isDestructive ? .error : .textPrimary)

                    if !subtitle.isEmpty {
                        Text(subtitle)
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                    }
                }

                Spacer()

                if !isDestructive {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.textSecondary)
                        .font(.caption)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.vertical, Theme.Spacing.xs)
    }
}

#Preview {
    ZStack {
        Color.backgroundDark.ignoresSafeArea()
        SettingsView()
    }
}
