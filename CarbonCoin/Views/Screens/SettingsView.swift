//
//  SettingsView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct SettingsView: View {
    var body: some View {
        NavigationStack {
            VStack {
                Text("设置页面")
                    .font(.largeTitle)
                    .foregroundColor(.green)
                
                Spacer()
                
                Text("好友管理、隐私设置、账户管理等功能")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
            }
            .navigationTitle("设置")
        }
    }
}

#Preview {
    SettingsView()
}
